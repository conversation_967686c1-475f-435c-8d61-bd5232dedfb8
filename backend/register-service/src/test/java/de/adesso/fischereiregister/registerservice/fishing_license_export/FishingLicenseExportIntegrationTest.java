package de.adesso.fischereiregister.registerservice.fishing_license_export;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewHandler;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.openapitools.model.ExportType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FishingLicenseExportReadIntegrationTest {

    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_SALT = "testSalt";
    private static final String TEST_OFFICE = "Test Office";

    // Test data IDs - using fixed UUIDs for consistent testing
    private static final UUID FISHING_LICENSE_REGISTER_ID = UUID.fromString("351031b4-d8ca-4380-a49f-47159ac68b8b");
    private static final String FISHING_LICENSE_DOCUMENT_ID = "3c56ecd4-23d3-46c3-806c-223323385b3b";

    private static final UUID TAX_REGISTER_ID = UUID.fromString("3049614a-61d9-4e14-b7cb-38ac25bf4d4c");
    private static final String TAX_DOCUMENT_ID = "a7f405d1-fcbf-4505-8303-47b14b5b8c80";
    private static final String TAX_DOCUMENT_ID_NO_TAX = "54cb8382-dac7-4bb9-8684-57172619eb18";

    private static final UUID LIMITED_LICENSE_REGISTER_ID = UUID.fromString("9652e547-879e-4385-ab04-72b8d366be45");
    private static final String LIMITED_LICENSE_DOCUMENT_ID = "3cfaf6ab-2caf-4239-9c2b-30eaee346408";

    @Autowired
    private RegisterEntryViewHandler registerEntryViewHandler;

    @Autowired
    private IdentificationDocumentViewHandler identificationDocumentViewHandler;

    @Autowired
    private MockMvc mvc;


    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_LICENSE
            	Verify that the PDF for FISHING_LICENSE is exported successfully.
            """)
    void testExportDigitizedFishingCertificateSuccess() throws Exception {
        final String documentId = "3c56ecd4-23d3-46c3-806c-223323385b3b";
        final UUID registerId = UUID.fromString("351031b4-d8ca-4380-a49f-47159ac68b8b");

        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/register-entries/" + registerId + "/identification-documents/"
                                + documentId
                                + "/pdf?type=" + ExportType.FISHING_LICENSE.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                .andExpect(status().isOk());

    }

    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=LIMITED_LICENSE_APPROVAL
            	Verify that the PDF for LIMITED_LICENSE_APPROVAL is exported successfully.
            """)
    void testExportLimitedLicenseApprovalSuccess() throws Exception {
        final String documentId = "3cfaf6ab-2caf-4239-9c2b-30eaee346408";
        final UUID registerId = UUID.fromString("9652e547-879e-4385-ab04-72b8d366be45");

        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/register-entries/" + registerId + "/identification-documents/"
                                + documentId
                                + "/pdf?type=" + ExportType.LIMITED_LICENSE_APPROVAL.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                .andExpect(status().isOk());

    }

    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_TAXES
            	Verify that the PDF for FISHING_TAXES is exported successfully.
            """)
    void testExportDigitizedFishingTaxSuccess() throws Exception {
        final String documentId = "a7f405d1-fcbf-4505-8303-47b14b5b8c80";
        final UUID registerId = UUID.fromString("3049614a-61d9-4e14-b7cb-38ac25bf4d4c");

        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/register-entries/" + registerId +
                                "/identification-documents/" + documentId +
                                "/pdf?type=" + ExportType.FISHING_TAXES.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                .andExpect(status().isOk());


    }

    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_TAXES
            	Verify that the PDF for FISHING_TAXES is exported successfully.
            """)
    void testExportPDFFailsDocumentFoundButNoTaxesInform() throws Exception {
        final String documentId = "54cb8382-dac7-4bb9-8684-57172619eb18";
        final UUID registerId = UUID.fromString("3049614a-61d9-4e14-b7cb-38ac25bf4d4c");

        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/register-entries/" + registerId +
                                "/identification-documents/" + documentId +
                                "/pdf?type=" + ExportType.FISHING_TAXES.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                .andExpect(status().isNotFound());

    }


    @Test
    @DisplayName("""
            	GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf
            	Verify that the API returns a not found status code for not existing ids.
            """)
    void testExportPDFFailsWithEntityNotFoundException() throws Exception {

        final UUID registerId = UUID.randomUUID();
        final String documentId = UUID.randomUUID().toString();

        mvc.perform(get(
                        "http://localhost:8080/register-entries/" + registerId +
                                "/identification-documents/" + documentId +
                                "/pdf?type=" + ExportType.FISHING_LICENSE.name()))
                .andExpect(status().isNotFound());
    }
}
